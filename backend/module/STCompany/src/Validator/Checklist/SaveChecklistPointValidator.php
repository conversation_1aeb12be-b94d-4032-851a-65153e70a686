<?php

namespace STCompany\Validator\Checklist;

use <PERSON><PERSON>\Validator\NumberComparison;
use <PERSON><PERSON>\Validator\StringLength;
use <PERSON><PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use STCompany\Data\ChecklistsPointsTable;
use STCompany\Data\ChecklistsTable;
use ST<PERSON>ib\Validator\Check\TextContent;
use STLib\Validator\Validator;

class SaveChecklistPointValidator extends Validator
{
    public function __construct(
        private readonly ChecklistsPointsTable $checklistsPointsTable,
        private readonly ChecklistsTable $checklistsTable,
    ) {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();
        $checklistPointId = $input['checklist_point_id'] ?? null;
        $checklistId = $input['checklist_id'] ? (int) $input['checklist_id'] : null;
        $companyId = $input['company_id'];

        if ($checklistId === null) {
            $this->addError('checklist_id', 'Checklist ID is required');
        }

        $textLengthValidator = new StringLength([
            'min' => 1,
            'max' => 1024,
        ]);
        $requiredTextProperties = [
            'title',
            'expected_actions',
            'good_performance_description',
            'bad_performance_description',
        ];

        foreach ($requiredTextProperties as $propertyName) {
            if (isset($input[$propertyName])) {
                if (!$textLengthValidator->isValid($input[$propertyName])) {
                    $this->addError($propertyName, 'The ' . $propertyName . 'should not exceed 1024 characters');
                }

                // Add text content validation
                $textContentValidator = new TextContent();

                if (!$textContentValidator->isValid($input[$propertyName])) {
                    $errors = $textContentValidator->getMessages();
                    foreach ($errors as $error) {
                        $this->addError($propertyName, $error);
                    }
                }
            } else {
                $this->addError($propertyName, 'The ' . $propertyName . ' is required');
            }
        }

        if ($this->hasError()) {
            return;
        }

        $checklistPointWithTitle = $this->checklistsPointsTable->getChecklistPointByTitleAndChecklistId(
            $input['title'],
            $checklistId
        );

        if (
            $checklistPointWithTitle !== null
            && $checklistPointId !== $checklistPointWithTitle->getId()
        ) {
            $this->addError('title', 'Checklist point with the title already exists in the checklist');
        }

        try {
            $this->checklistsTable->getChecklist($checklistId, $companyId);
        } catch (NotFoundApiException $e) {
            $this->addError('checklist_id', 'Checklist not found');
        }

        if ($checklistPointId !== null) {
            try {
                $this->checklistsPointsTable->getChecklistPoint($checklistPointId, $companyId);
            } catch (NotFoundApiException $e) {
                $this->addError('checklist_point_id', 'Checklist point not found');
            }
        }
    }
}
