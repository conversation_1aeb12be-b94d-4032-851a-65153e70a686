<?php

namespace STLib\Validator\Check;

use Lam<PERSON>\Validator\AbstractValidator;

class TextContent extends AbstractValidator
{
    const TOO_SHORT = 'tooShort';
    const DISALLOWED_CHARACTERS = 'disallowedCharacters';
    const CONSECUTIVE_WHITESPACE = 'consecutiveWhitespace';
    const MEANINGLESS_CONTENT = 'meaninglessContent';

    /**
     * @var array
     */
    protected $messageTemplates = [
        self::TOO_SHORT => '%fieldName% must be at least 12 characters long.',
        self::DISALLOWED_CHARACTERS => 'Some characters are not allowed. Please use only letters, numbers, basic punctuation (_ - . , ! ? ; : — \' "), spaces, and line breaks.',
        self::CONSECUTIVE_WHITESPACE => 'Please avoid using multiple spaces or empty lines in a row.',
        self::MEANINGLESS_CONTENT => '%fieldName% must contain meaningful content, not just punctuation or spaces.',
    ];

    /**
     * @var string
     */
    protected string $fieldName = 'Field';

    /**
     * Allowed characters pattern
     * Letters (A-Z, a-z), Numbers (0-9), Spaces, Single line breaks (\n)
     * Punctuation: _ - . , ! ? ; : " ' and Em dash (—)
     */
    private const ALLOWED_CHARACTERS_PATTERN = '/^[A-Za-z0-9\s\n_\-.,!?;:"\'—]*$/u';

    /**
     * Pattern to detect consecutive spaces or line breaks
     */
    private const CONSECUTIVE_WHITESPACE_PATTERN = '/  +|\n\n+/';

    /**
     * Pattern to check if content has meaningful text (at least 6 letters)
     */
    private const MEANINGFUL_CONTENT_PATTERN = '/[A-Za-z]/';

    /**
     * Set the field name for error messages
     *
     * @param string $fieldName
     * @return self
     */
    public function setFieldName(string $fieldName): self
    {
        $this->fieldName = $fieldName;
        return $this;
    }

    /**
     * Get the field name
     *
     * @return string
     */
    public function getFieldName(): string
    {
        return $this->fieldName;
    }

    /**
     * Clean the input text
     *
     * @param string $value
     * @return string
     */
    private function cleanText(string $value): string
    {
        // Trim leading/trailing whitespace
        $cleaned = trim($value);
        
        // Replace multiple spaces with single space
        $cleaned = preg_replace('/  +/', ' ', $cleaned);
        
        // Replace multiple line breaks with single line break
        $cleaned = preg_replace('/\n\n+/', "\n", $cleaned);
        
        return $cleaned;
    }

    /**
     * Count letters in the text
     *
     * @param string $value
     * @return int
     */
    private function countLetters(string $value): int
    {
        return preg_match_all('/[A-Za-z]/', $value);
    }

    /**
     * Check if content is meaningful (not just punctuation or spaces)
     *
     * @param string $value
     * @return bool
     */
    private function hasMeaningfulContent(string $value): bool
    {
        // Remove all allowed punctuation and whitespace
        $contentOnly = preg_replace('/[\s\n_\-.,!?;:"\'—]/', '', $value);
        
        // Check if there's any alphanumeric content left
        return !empty($contentOnly) && preg_match('/[A-Za-z0-9]/', $contentOnly);
    }

    /**
     * Validate the text content
     *
     * @param mixed $value
     * @return bool
     */
    public function isValid($value): bool
    {
        if (!is_string($value)) {
            $value = (string) $value;
        }

        $this->setValue($value);

        // Check for disallowed characters first (before cleaning)
        if (!preg_match(self::ALLOWED_CHARACTERS_PATTERN, $value)) {
            $this->error(self::DISALLOWED_CHARACTERS);
            return false;
        }

        // Check for consecutive whitespace (before cleaning)
        if (preg_match(self::CONSECUTIVE_WHITESPACE_PATTERN, $value)) {
            $this->error(self::CONSECUTIVE_WHITESPACE);
            return false;
        }

        // Clean the text
        $cleanedValue = $this->cleanText($value);

        // Check minimum length after cleanup
        if (mb_strlen($cleanedValue) < 12) {
            $this->error(self::TOO_SHORT);
            return false;
        }

        // Check for meaningful content
        if (!$this->hasMeaningfulContent($cleanedValue)) {
            $this->error(self::MEANINGLESS_CONTENT);
            return false;
        }

        // Check minimum letter count (at least 6 letters)
        if ($this->countLetters($cleanedValue) < 6) {
            $this->error(self::MEANINGLESS_CONTENT);
            return false;
        }

        return true;
    }

    /**
     * Override error method to include field name in messages
     *
     * @param string $messageKey
     * @param mixed $value
     * @return void
     */
    protected function error($messageKey, $value = null): void
    {
        if ($value === null) {
            $value = $this->getValue();
        }

        $message = $this->messageTemplates[$messageKey];
        $message = str_replace('%fieldName%', $this->fieldName, $message);
        
        $this->abstractOptions['messages'][$messageKey] = $message;
        $this->abstractOptions['messageVariables']['fieldName'] = 'fieldName';
        
        parent::error($messageKey, $value);
    }
}
